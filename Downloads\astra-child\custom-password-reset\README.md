# Custom Password Reset Plugin

Plugin personalizado para customizar a tela de "esqueceu a senha" do WordPress com design white label e totalmente customizável.

## 🚀 Características

- **Design White Label**: Remove completamente a aparência padrão do WordPress
- **Modo Dark/Light**: Alternância entre temas escuro e claro
- **Totalmente Customizável**: Cores, textos, logo e estilos personalizáveis
- **Responsivo**: Funciona perfeitamente em dispositivos móveis
- **Acessível**: Suporte completo a leitores de tela e navegação por teclado
- **Integração Perfeita**: Integra-se perfeitamente com o tema Astra Child

## 📁 Estrutura do Plugin

```
custom-password-reset/
├── custom-password-reset.php    # Arquivo principal do plugin
├── assets/
│   ├── css/
│   │   ├── password-reset.css   # Estilos principais
│   │   ├── login-override.css   # Override da página de login
│   │   └── admin.css           # Estilos do painel administrativo
│   └── js/
│       ├── password-reset.js    # JavaScript principal
│       ├── login-override.js    # JavaScript para login
│       └── admin.js            # JavaScript do painel
├── templates/
│   ├── lost-password-form.php   # Template do formulário "esqueceu senha"
│   ├── reset-password-form.php  # Template do formulário de redefinição
│   └── admin-page.php          # Template da página administrativa
└── README.md                   # Este arquivo
```

## 🎨 Funcionalidades de Design

### Modo Dark (Padrão)
- Fundo escuro (#1a1a1a)
- Card em tom de cinza (#2d2d2d)
- Texto branco com boa legibilidade
- Botão azul moderno (#4a90e2)

### Modo Light
- Fundo claro (#f5f5f5)
- Card branco com sombra sutil
- Texto escuro para melhor contraste
- Mantém o botão azul para consistência

### Elementos Customizáveis
- **Logo**: Upload de imagem personalizada ou ícone padrão
- **Cores**: Fundo, card, texto, botão (todas customizáveis)
- **Textos**: Título, subtítulo, botão, links
- **CSS/JS**: Código personalizado para ajustes avançados

## ⚙️ Configuração

### Painel Administrativo
Acesse **Configurações > Password Reset** no WordPress admin para:

1. **Aba Geral**:
   - Definir tema padrão (Dark/Light)
   - Upload da logo
   - Configurar página personalizada

2. **Aba Aparência**:
   - Personalizar todas as cores
   - Seletor de cores integrado

3. **Aba Textos**:
   - Customizar todos os textos
   - Título, subtítulo, botões

4. **Aba Avançado**:
   - CSS personalizado
   - JavaScript personalizado

5. **Aba Visualizar**:
   - Preview em tempo real
   - Teste de diferentes temas

### Integração com o Tema
O plugin é automaticamente carregado pelo `functions.php` do tema:

```php
$this->require_if_exists(get_stylesheet_directory() . '/custom-password-reset/custom-password-reset.php');
```

## 🔧 Funcionalidades Técnicas

### Interceptação de Formulários
- Intercepta `wp-login.php?action=lostpassword`
- Intercepta `wp-login.php?action=resetpass`
- Redireciona para templates personalizados

### Processamento Seguro
- Usa nonces do WordPress para segurança
- Validação de dados no frontend e backend
- Sanitização de todos os inputs

### Compatibilidade
- WordPress 5.0+
- PHP 7.4+
- Todos os navegadores modernos
- Dispositivos móveis

### Acessibilidade
- ARIA labels e roles
- Navegação por teclado
- Suporte a leitores de tela
- Alto contraste
- Redução de movimento

## 🎯 Como Usar

### Para Usuários Finais
1. Acesse a página de login do site
2. Clique em "Esqueceu a senha?"
3. Use o formulário personalizado
4. Alterne entre modo dark/light com o botão no canto superior direito

### Para Administradores
1. Vá para **Configurações > Password Reset**
2. Personalize cores, textos e logo
3. Salve as configurações
4. Visualize o resultado na aba "Visualizar"

## 🔄 Integração com Outros Plugins

### PowerPack Login Form
O plugin funciona em conjunto com o PowerPack, interceptando os emails de redefinição de senha.

### Redirection Manager
Integra-se com o sistema de redirecionamentos do tema para páginas personalizadas.

### Cache Plugins
Configurado para não fazer cache das páginas de login e reset de senha.

## 🛠️ Desenvolvimento

### Hooks Disponíveis
```php
// Antes de renderizar o formulário
do_action('cpr_before_form_render', $settings);

// Após processar o formulário
do_action('cpr_after_form_process', $result, $user_data);

// Filtrar configurações
$settings = apply_filters('cpr_settings', $settings);
```

### Classes CSS Principais
```css
.cpr-password-reset     /* Body principal */
.cpr-theme-dark         /* Tema escuro */
.cpr-theme-light        /* Tema claro */
.cpr-container          /* Container principal */
.cpr-card               /* Card do formulário */
.cpr-form               /* Formulário */
.cpr-button             /* Botão principal */
.cpr-theme-toggle       /* Botão de alternar tema */
```

### JavaScript API
```javascript
// Alterar tema programaticamente
window.customPasswordReset.setTheme('dark');

// Obter tema atual
const theme = window.customPasswordReset.getTheme();
```

## 📱 Responsividade

O plugin é totalmente responsivo com breakpoints:
- **Desktop**: > 768px
- **Tablet**: 481px - 768px
- **Mobile**: < 480px

Ajustes automáticos:
- Tamanho de fonte
- Padding e margens
- Tamanho de botões
- Posicionamento de elementos

## 🔒 Segurança

- Nonces em todos os formulários
- Sanitização de dados
- Validação server-side
- Escape de output
- Rate limiting (via WordPress)

## 🐛 Troubleshooting

### Problema: Formulário não aparece personalizado
**Solução**: Verifique se o plugin está ativado e os arquivos CSS estão carregando.

### Problema: Tema não alterna
**Solução**: Verifique se o JavaScript está carregando e não há conflitos.

### Problema: Configurações não salvam
**Solução**: Verifique permissões de usuário e nonces.

## 📄 Licença

Este plugin é parte do tema Astra Child e segue a mesma licença.

## 🤝 Suporte

Para suporte, entre em contato com a equipe de desenvolvimento do tema.

---

**Versão**: 1.0.0  
**Compatibilidade**: WordPress 5.0+  
**Testado até**: WordPress 6.4  
**Autor**: Astra Child Theme Team
