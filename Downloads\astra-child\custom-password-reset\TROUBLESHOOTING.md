# Troubleshooting - Custom Password Reset

## 🚨 Problemas Identificados e Soluções

### 1. **Design Quebrado na Página de Reset**

**Problema**: A página de reset aparece sem estilos, apenas com HTML básico.

**<PERSON><PERSON><PERSON> Possíveis**:
- CSS não está sendo carregado
- Caminhos dos arquivos incorretos
- Conflito com outros plugins/temas

**Soluções**:

#### Solução 1: Verificar Caminhos dos Arquivos
```bash
# Verificar se os arquivos existem
ls -la Downloads/astra-child/custom-password-reset/assets/css/
ls -la Downloads/astra-child/custom-password-reset/templates/
```

#### Solução 2: Testar CSS Inline
O plugin já foi atualizado para carregar CSS inline nos templates. Isso garante que os estilos sejam aplicados mesmo se houver problemas de carregamento.

#### Solução 3: Verificar Permissões
```bash
# Dar permissões corretas aos arquivos
chmod 644 Downloads/astra-child/custom-password-reset/assets/css/*.css
chmod 644 Downloads/astra-child/custom-password-reset/assets/js/*.js
chmod 644 Downloads/astra-child/custom-password-reset/templates/*.php
```

#### Solução 4: Debug Manual
Use o arquivo `debug.php` para testar:
```bash
# Abrir no navegador
http://localhost:8000/debug.php
```

### 2. **Preview Vazio no Painel Administrativo**

**Problema**: A aba "Visualizar" não mostra nada ou mostra erro.

**Causas Possíveis**:
- JavaScript não está carregando
- Problemas com iframe
- Conflitos de CORS

**Soluções**:

#### Solução 1: Verificar Console do Navegador
1. Abrir DevTools (F12)
2. Ir para aba Console
3. Procurar por erros JavaScript

#### Solução 2: Testar Preview Manualmente
```javascript
// No console do navegador, na página do admin
if (window.cprAdmin) {
    window.cprAdmin.updatePreview();
}
```

#### Solução 3: Usar Página de Teste
Em vez do preview, use a página de teste:
```
http://seu-site.com/wp-content/themes/astra-child/custom-password-reset/test-page.html
```

### 3. **Plugin Não Intercepta URLs de Reset**

**Problema**: Ao clicar em "Esqueceu a senha", ainda aparece a tela padrão do WordPress.

**Soluções**:

#### Solução 1: Verificar Hooks
Adicione este código temporário no `functions.php` para debug:
```php
add_action('init', function() {
    if (isset($_GET['action']) && $_GET['action'] === 'lostpassword') {
        error_log('Hook de lostpassword ativado');
    }
});
```

#### Solução 2: Forçar Redirecionamento
Adicione no `functions.php`:
```php
add_action('login_form_lostpassword', function() {
    wp_redirect(home_url('/esqueceu-senha/'));
    exit;
});
```

### 4. **Configurações Não Salvam**

**Problema**: Mudanças no painel administrativo não são salvas.

**Soluções**:

#### Solução 1: Verificar AJAX
1. Abrir DevTools → Network
2. Tentar salvar configurações
3. Verificar se requisição AJAX é enviada

#### Solução 2: Verificar Nonces
```php
// Adicionar debug no método save_settings_ajax
error_log('Nonce recebido: ' . $_POST['nonce']);
error_log('Nonce esperado: ' . wp_create_nonce('cpr_admin_nonce'));
```

#### Solução 3: Verificar Permissões
```php
// Verificar se usuário tem permissões
if (!current_user_can('manage_options')) {
    error_log('Usuário sem permissões para salvar configurações');
}
```

## 🔧 Comandos de Debug Úteis

### Verificar se Plugin Está Carregado
```php
// Adicionar no functions.php temporariamente
add_action('wp_footer', function() {
    if (class_exists('CustomPasswordReset')) {
        echo '<!-- Custom Password Reset Plugin Carregado -->';
    } else {
        echo '<!-- Custom Password Reset Plugin NÃO Carregado -->';
    }
});
```

### Verificar Configurações
```php
// No wp-admin, ir para Ferramentas → Site Health → Info
// Ou adicionar temporariamente:
add_action('wp_footer', function() {
    if (is_admin()) {
        echo '<script>console.log("CPR Settings:", ' . json_encode([
            'theme' => get_option('cpr_theme'),
            'logo_url' => get_option('cpr_logo_url'),
            'background_color' => get_option('cpr_background_color')
        ]) . ');</script>';
    }
});
```

### Verificar Assets
```php
// Verificar se CSS está sendo enfileirado
add_action('wp_print_styles', function() {
    global $wp_styles;
    if (isset($wp_styles->registered['custom-password-reset-style'])) {
        error_log('CSS do Custom Password Reset está registrado');
    }
});
```

## 🚀 Soluções Rápidas

### Solução Rápida 1: Reset Completo
```bash
# Desativar e reativar o plugin
# No wp-admin: Plugins → Desativar → Ativar
```

### Solução Rápida 2: Limpar Cache
```php
// Adicionar temporariamente no functions.php
add_action('init', function() {
    if (isset($_GET['clear_cpr_cache'])) {
        wp_cache_flush();
        delete_transient('cpr_settings');
        echo 'Cache limpo!';
        exit;
    }
});
// Depois acessar: http://seu-site.com/?clear_cpr_cache=1
```

### Solução Rápida 3: Forçar Recarga de Assets
```php
// No custom-password-reset.php, alterar temporariamente:
wp_enqueue_style(
    'custom-password-reset-style',
    $this->plugin_url . 'assets/css/password-reset.css',
    [],
    time() // Força recarga
);
```

## 📋 Checklist de Verificação

- [ ] Arquivos CSS existem em `assets/css/`
- [ ] Arquivos JS existem em `assets/js/`
- [ ] Templates existem em `templates/`
- [ ] Plugin está sendo carregado no `functions.php`
- [ ] Não há erros no console do navegador
- [ ] Não há erros no log do WordPress
- [ ] Permissões dos arquivos estão corretas
- [ ] Cache foi limpo
- [ ] Outros plugins não estão conflitando

## 🆘 Se Nada Funcionar

### Opção 1: Usar Apenas CSS Override
Se o plugin não funcionar, você pode usar apenas o CSS override:
```php
// Adicionar no functions.php
add_action('login_enqueue_scripts', function() {
    wp_enqueue_style('custom-login', get_stylesheet_directory_uri() . '/custom-password-reset/assets/css/login-override.css');
});
```

### Opção 2: Criar Página Personalizada
1. Criar uma página no WordPress
2. Usar o shortcode: `[custom_password_reset]`
3. Redirecionar links de "esqueceu senha" para essa página

### Opção 3: Usar Plugin Externo
Como alternativa, considere usar plugins como:
- LoginPress
- Custom Login Page Customizer
- Ultimate Member

## 📞 Suporte

Se os problemas persistirem:
1. Ativar WP_DEBUG no wp-config.php
2. Verificar logs em wp-content/debug.log
3. Testar com tema padrão (Twenty Twenty-Three)
4. Desativar outros plugins temporariamente
5. Verificar versão do PHP e WordPress

---

**Última atualização**: 2024-01-XX  
**Versão do Plugin**: 1.0.0
