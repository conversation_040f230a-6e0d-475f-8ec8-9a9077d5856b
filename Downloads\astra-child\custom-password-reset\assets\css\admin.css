/* Custom Password Reset Admin Styles */

.cpr-admin-wrap {
    max-width: 1200px;
    margin: 0;
}

.cpr-admin-wrap h1 {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    margin-bottom: 20px;
}

.cpr-admin-wrap h1 .dashicons {
    font-size: 28px;
    width: 28px;
    height: 28px;
    margin-right: 0;
}

.cpr-admin-header {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 6px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.cpr-admin-header p {
    margin: 0;
    font-size: 16px;
    color: #666;
}

.cpr-admin-container {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 6px;
    margin: 20px 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Navigation Tabs */
.cpr-nav-tabs {
    border-bottom: 1px solid #ccd0d4;
    margin: 0;
    padding: 0 20px;
    background: #f9f9f9;
}

.cpr-nav-tabs .nav-tab {
    border: none;
    border-bottom: 3px solid transparent;
    margin-bottom: 0;
    padding: 15px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.cpr-nav-tabs .nav-tab:hover {
    background: #e9ecef;
    color: #0073aa;
}

.cpr-nav-tabs .nav-tab.nav-tab-active {
    background: #fff;
    border-bottom-color: #0073aa;
    color: #0073aa;
}

.cpr-nav-tabs .nav-tab .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-right: 0;
}

/* Tab Content */
.cpr-tab-content {
    padding: 30px;
    min-height: 400px;
}

.cpr-card {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 6px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.cpr-card h2 {
    margin: 0 0 20px 0;
    padding-bottom: 15px;
    border-bottom: 2px solid #f1f1f1;
    font-size: 20px;
    color: #333;
}

/* Form Elements */
.form-table th {
    width: 200px;
    padding: 20px 10px 20px 0;
    vertical-align: top;
}

.form-table td {
    padding: 20px 0;
}

.form-table .description {
    margin-top: 8px;
    font-style: italic;
    color: #666;
}

/* Color Picker */
.wp-picker-container {
    display: inline-block;
}

.wp-picker-container .wp-color-result {
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Upload Button */
.cpr-upload-button {
    margin-left: 10px;
    vertical-align: top;
}

/* Conditional Fields */
.cpr-conditional {
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.cpr-conditional.show {
    display: table-row;
    opacity: 1;
}

/* Preview simples - apenas o necessário */

/* Action Buttons */
.cpr-admin-actions {
    padding: 25px 30px;
    border-top: 1px solid #ccd0d4;
    background: #f9f9f9;
    display: flex;
    align-items: center;
    gap: 15px;
}

.cpr-admin-actions .button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    font-weight: 500;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.cpr-admin-actions .button-primary {
    background: #0073aa;
    border-color: #0073aa;
    box-shadow: 0 2px 4px rgba(0, 115, 170, 0.3);
}

.cpr-admin-actions .button-primary:hover {
    background: #005a87;
    border-color: #005a87;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 115, 170, 0.4);
}

.cpr-admin-actions .button-secondary {
    background: #f7f7f7;
    border-color: #ccd0d4;
    color: #555;
}

.cpr-admin-actions .button-secondary:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

/* Status Messages */
.cpr-admin-status {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    border-radius: 4px;
    font-weight: 500;
    animation: slideIn 0.3s ease;
}

.cpr-admin-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.cpr-admin-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.cpr-admin-status::before {
    font-family: 'dashicons';
    font-size: 16px;
}

.cpr-admin-status.success::before {
    content: '\f147'; /* checkmark */
}

.cpr-admin-status.error::before {
    content: '\f534'; /* warning */
}

/* Loading Animation */
.dashicons.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Code Editor */
.code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    padding: 15px;
}

/* Responsive Design */
@media (max-width: 782px) {
    .cpr-nav-tabs {
        padding: 0 10px;
    }

    .cpr-nav-tabs .nav-tab {
        padding: 12px 15px;
        font-size: 14px;
    }

    .cpr-tab-content {
        padding: 20px 15px;
    }

    .cpr-card {
        padding: 20px 15px;
    }

    .cpr-admin-actions {
        padding: 20px 15px;
        flex-direction: column;
        align-items: stretch;
    }

    .cpr-admin-actions .button {
        justify-content: center;
        margin-bottom: 10px;
    }

    .form-table th,
    .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }

    .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }


}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .cpr-admin-container,
    .cpr-card {
        background: #1e1e1e;
        border-color: #3c3c3c;
        color: #e0e0e0;
    }
    
    .cpr-nav-tabs {
        background: #2a2a2a;
    }
    
    .cpr-nav-tabs .nav-tab.nav-tab-active {
        background: #1e1e1e;
        color: #4a9eff;
    }
    
    .cpr-card h2 {
        color: #e0e0e0;
        border-bottom-color: #3c3c3c;
    }
    
    .form-table .description {
        color: #b0b0b0;
    }
    
    .code {
        background: #2a2a2a;
        border-color: #3c3c3c;
        color: #e0e0e0;
    }
}

/* Accessibility */
.cpr-nav-tabs .nav-tab:focus {
    outline: 2px solid #0073aa;
    outline-offset: -2px;
}

.button:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .cpr-nav-tabs .nav-tab.nav-tab-active {
        border-bottom-width: 4px;
    }
    
    .cpr-admin-status {
        border-width: 2px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .cpr-admin-actions .button-primary:hover {
        transform: none;
    }
}
