<?php
/**
 * Template da página de administração
 */

if (!defined('ABSPATH')) {
    exit('Acesso direto não permitido');
}
?>

<div class="wrap cpr-admin-wrap">
    <h1>
        <i class="dashicons dashicons-lock"></i>
        Custom Password Reset
    </h1>
    
    <div class="cpr-admin-header">
        <p>Configure a aparência e comportamento da tela de redefinição de senha personalizada.</p>
    </div>
    
    <div class="cpr-admin-container">
        <!-- Navegação por abas -->
        <nav class="nav-tab-wrapper cpr-nav-tabs">
            <a href="#general" class="nav-tab nav-tab-active" data-tab="general">
                <i class="dashicons dashicons-admin-generic"></i>
                Geral
            </a>
            <a href="#appearance" class="nav-tab" data-tab="appearance">
                <i class="dashicons dashicons-admin-appearance"></i>
                Aparência
            </a>
            <a href="#texts" class="nav-tab" data-tab="texts">
                <i class="dashicons dashicons-editor-textcolor"></i>
                Textos
            </a>
            <a href="#advanced" class="nav-tab" data-tab="advanced">
                <i class="dashicons dashicons-admin-tools"></i>
                Avançado
            </a>
            <a href="#preview" class="nav-tab" data-tab="preview">
                <i class="dashicons dashicons-visibility"></i>
                Visualizar
            </a>
        </nav>
        
        <!-- Formulário principal -->
        <form id="cpr-settings-form" method="post">
            <?php wp_nonce_field('cpr_admin_nonce', 'nonce'); ?>
            
            <!-- Aba Geral -->
            <div class="cpr-tab-content" id="tab-general">
                <div class="cpr-card">
                    <h2>Configurações Gerais</h2>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="theme">Tema Padrão</label>
                            </th>
                            <td>
                                <select name="settings[theme]" id="theme" class="regular-text">
                                    <option value="dark" <?php selected($settings['theme'], 'dark'); ?>>Escuro</option>
                                    <option value="light" <?php selected($settings['theme'], 'light'); ?>>Claro</option>
                                </select>
                                <p class="description">Tema padrão que será carregado inicialmente.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="logo_url">URL da Logo</label>
                            </th>
                            <td>
                                <input type="url" name="settings[logo_url]" id="logo_url" value="<?php echo esc_attr($settings['logo_url']); ?>" class="regular-text">
                                <button type="button" class="button cpr-upload-button" data-target="logo_url">
                                    Selecionar Imagem
                                </button>
                                <p class="description">Logo que aparecerá no topo do formulário. Se vazio, será usado um ícone padrão.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="enable_custom_page">Página Personalizada</label>
                            </th>
                            <td>
                                <label>
                                    <input type="checkbox" name="settings[enable_custom_page]" id="enable_custom_page" value="1" <?php checked($settings['enable_custom_page'], true); ?>>
                                    Usar página personalizada em vez da padrão do WordPress
                                </label>
                                <p class="description">Quando ativado, redirecionará para uma página personalizada.</p>
                            </td>
                        </tr>
                        
                        <tr class="cpr-conditional" data-condition="enable_custom_page" data-value="1">
                            <th scope="row">
                                <label for="custom_page_slug">Slug da Página</label>
                            </th>
                            <td>
                                <input type="text" name="settings[custom_page_slug]" id="custom_page_slug" value="<?php echo esc_attr($settings['custom_page_slug']); ?>" class="regular-text">
                                <p class="description">Slug da página personalizada (ex: esqueceu-senha).</p>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- Aba Aparência -->
            <div class="cpr-tab-content" id="tab-appearance" style="display: none;">
                <div class="cpr-card">
                    <h2>Cores e Estilo</h2>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="background_color">Cor de Fundo</label>
                            </th>
                            <td>
                                <input type="text" name="settings[background_color]" id="background_color" value="<?php echo esc_attr($settings['background_color']); ?>" class="cpr-color-picker">
                                <p class="description">Cor de fundo da página.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="card_background">Cor do Card</label>
                            </th>
                            <td>
                                <input type="text" name="settings[card_background]" id="card_background" value="<?php echo esc_attr($settings['card_background']); ?>" class="cpr-color-picker">
                                <p class="description">Cor de fundo do formulário.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="text_color">Cor do Texto</label>
                            </th>
                            <td>
                                <input type="text" name="settings[text_color]" id="text_color" value="<?php echo esc_attr($settings['text_color']); ?>" class="cpr-color-picker">
                                <p class="description">Cor principal do texto.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="button_color">Cor do Botão</label>
                            </th>
                            <td>
                                <input type="text" name="settings[button_color]" id="button_color" value="<?php echo esc_attr($settings['button_color']); ?>" class="cpr-color-picker">
                                <p class="description">Cor de fundo do botão principal.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="button_text_color">Cor do Texto do Botão</label>
                            </th>
                            <td>
                                <input type="text" name="settings[button_text_color]" id="button_text_color" value="<?php echo esc_attr($settings['button_text_color']); ?>" class="cpr-color-picker">
                                <p class="description">Cor do texto do botão principal.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="button_hover_color">Cor do Botão ao Passar o Mouse</label>
                            </th>
                            <td>
                                <input type="text" name="settings[button_hover_color]" id="button_hover_color" value="<?php echo esc_attr($settings['button_hover_color']); ?>" class="cpr-color-picker">
                                <p class="description">Cor do botão quando o usuário passa o mouse por cima (hover).</p>
                            </td>
                        </tr>


                    </table>
                </div>
            </div>
            
            <!-- Aba Textos -->
            <div class="cpr-tab-content" id="tab-texts" style="display: none;">
                <div class="cpr-card">
                    <h2>Personalização de Textos</h2>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="title">Título Principal</label>
                            </th>
                            <td>
                                <input type="text" name="settings[title]" id="title" value="<?php echo esc_attr($settings['title']); ?>" class="regular-text">
                                <p class="description">Título que aparece no topo do formulário.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="subtitle">Subtítulo</label>
                            </th>
                            <td>
                                <textarea name="settings[subtitle]" id="subtitle" rows="3" class="large-text"><?php echo esc_textarea($settings['subtitle']); ?></textarea>
                                <p class="description">Texto explicativo abaixo do título.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="button_text">Texto do Botão</label>
                            </th>
                            <td>
                                <input type="text" name="settings[button_text]" id="button_text" value="<?php echo esc_attr($settings['button_text']); ?>" class="regular-text">
                                <p class="description">Texto do botão de envio.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="back_to_login_text">Texto "Voltar ao Login"</label>
                            </th>
                            <td>
                                <input type="text" name="settings[back_to_login_text]" id="back_to_login_text" value="<?php echo esc_attr($settings['back_to_login_text']); ?>" class="regular-text">
                                <p class="description">Texto do link para voltar ao login.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="success_message">Mensagem de Sucesso</label>
                            </th>
                            <td>
                                <textarea name="settings[success_message]" id="success_message" rows="3" class="large-text"><?php echo esc_textarea($settings['success_message']); ?></textarea>
                                <p class="description">Mensagem exibida quando o e-mail de reset é enviado com sucesso.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="input_placeholder">Placeholder do Campo E-mail</label>
                            </th>
                            <td>
                                <input type="text" name="settings[input_placeholder]" id="input_placeholder" value="<?php echo esc_attr($settings['input_placeholder']); ?>" class="regular-text">
                                <p class="description">Texto de exemplo que aparece dentro do campo de e-mail.</p>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- Aba Avançado -->
            <div class="cpr-tab-content" id="tab-advanced" style="display: none;">
                <div class="cpr-card">
                    <h2>Configurações Avançadas</h2>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">CSS Personalizado</th>
                            <td>
                                <textarea name="settings[custom_css]" id="custom_css" rows="10" class="large-text code"><?php echo esc_textarea($settings['custom_css'] ?? ''); ?></textarea>
                                <p class="description">CSS personalizado que será aplicado à página de reset de senha.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">JavaScript Personalizado</th>
                            <td>
                                <textarea name="settings[custom_js]" id="custom_js" rows="10" class="large-text code"><?php echo esc_textarea($settings['custom_js'] ?? ''); ?></textarea>
                                <p class="description">JavaScript personalizado (sem as tags &lt;script&gt;).</p>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- Aba Preview -->
            <div class="cpr-tab-content" id="tab-preview" style="display: none;">
                <div class="cpr-card">
                    <h2>Visualização</h2>
                    <p>Clique no link abaixo para visualizar a página de reset de senha:</p>

                    <p>
                        <a href="<?php echo wp_login_url() . '?action=lostpassword'; ?>"
                           target="_blank"
                           class="button button-primary button-large">
                            Visualizar Página de Reset
                        </a>
                    </p>
                </div>
            </div>
            
            <!-- Botões de ação -->
            <div class="cpr-admin-actions">
                <button type="submit" class="button button-primary button-large" id="cpr-save-settings">
                    <i class="dashicons dashicons-yes"></i>
                    Salvar Configurações
                </button>
                
                <button type="button" class="button button-secondary" id="cpr-reset-settings">
                    <i class="dashicons dashicons-undo"></i>
                    Restaurar Padrões
                </button>
                
                <div class="cpr-admin-status" id="cpr-status" style="display: none;"></div>
            </div>
        </form>
    </div>
</div>

<!-- CSS da página de administração -->
<style>
.cpr-admin-wrap {
    max-width: 1200px;
}

.cpr-admin-header {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}

.cpr-admin-container {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    margin: 20px 0;
}

.cpr-nav-tabs {
    border-bottom: 1px solid #ccd0d4;
    margin: 0;
    padding: 0 20px;
}

.cpr-nav-tabs .nav-tab {
    border-bottom: 1px solid transparent;
    margin-bottom: -1px;
}

.cpr-nav-tabs .nav-tab.nav-tab-active {
    border-bottom-color: #fff;
}

.cpr-tab-content {
    padding: 20px;
}

.cpr-card {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.cpr-card h2 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #e1e1e1;
}

.cpr-admin-actions {
    padding: 20px;
    border-top: 1px solid #ccd0d4;
    background: #f9f9f9;
}

.cpr-admin-status {
    display: inline-block;
    margin-left: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    font-weight: 500;
}

.cpr-admin-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.cpr-admin-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.cpr-conditional {
    display: none;
}

.cpr-conditional.show {
    display: table-row;
}

.cpr-preview-container {
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    overflow: hidden;
    margin: 20px 0;
}

.cpr-upload-button {
    margin-left: 10px;
}

.dashicons {
    margin-right: 5px;
}
</style>

<!-- JavaScript da página de administração -->
<script>
jQuery(document).ready(function($) {
    // Inicializar color pickers
    $('.cpr-color-picker').wpColorPicker();
    
    // Navegação por abas
    $('.cpr-nav-tabs .nav-tab').on('click', function(e) {
        e.preventDefault();
        
        const tab = $(this).data('tab');
        
        // Atualizar abas ativas
        $('.nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');
        
        // Mostrar conteúdo da aba
        $('.cpr-tab-content').hide();
        $('#tab-' + tab).show();
        
        // Atualizar URL
        window.location.hash = tab;
    });
    
    // Carregar aba da URL
    if (window.location.hash) {
        const hash = window.location.hash.substring(1);
        $('.nav-tab[data-tab="' + hash + '"]').click();
    }
    
    // Campos condicionais
    function toggleConditionalFields() {
        $('.cpr-conditional').each(function() {
            const $row = $(this);
            const condition = $row.data('condition');
            const value = $row.data('value');
            const $field = $('#' + condition);
            
            if ($field.is(':checkbox')) {
                if (($field.is(':checked') && value == '1') || (!$field.is(':checked') && value == '0')) {
                    $row.addClass('show');
                } else {
                    $row.removeClass('show');
                }
            } else {
                if ($field.val() == value) {
                    $row.addClass('show');
                } else {
                    $row.removeClass('show');
                }
            }
        });
    }
    
    // Executar ao carregar e quando campos mudarem
    toggleConditionalFields();
    $('input, select').on('change', toggleConditionalFields);
    
    // Upload de imagem
    $('.cpr-upload-button').on('click', function(e) {
        e.preventDefault();
        
        const button = $(this);
        const targetField = button.data('target');
        
        const mediaUploader = wp.media({
            title: 'Selecionar Logo',
            button: {
                text: 'Usar esta imagem'
            },
            multiple: false
        });
        
        mediaUploader.on('select', function() {
            const attachment = mediaUploader.state().get('selection').first().toJSON();
            $('#' + targetField).val(attachment.url);
        });
        
        mediaUploader.open();
    });
    
    // Salvar configurações
    $('#cpr-settings-form').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $button = $('#cpr-save-settings');
        const $status = $('#cpr-status');
        
        $button.prop('disabled', true).find('.dashicons').addClass('spin');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: $form.serialize() + '&action=cpr_save_settings',
            success: function(response) {
                if (response.success) {
                    $status.removeClass('error').addClass('success').text(response.data).show();
                } else {
                    $status.removeClass('success').addClass('error').text(response.data || 'Erro ao salvar').show();
                }
            },
            error: function() {
                $status.removeClass('success').addClass('error').text('Erro de conexão').show();
            },
            complete: function() {
                $button.prop('disabled', false).find('.dashicons').removeClass('spin');
                setTimeout(function() {
                    $status.fadeOut();
                }, 3000);
            }
        });
    });
    
    // Restaurar padrões
    $('#cpr-reset-settings').on('click', function() {
        if (confirm('Tem certeza que deseja restaurar todas as configurações para os valores padrão?')) {
            const $button = $(this);
            const $status = $('#cpr-status');
            
            $button.prop('disabled', true);
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'cpr_reset_settings',
                    nonce: $('input[name="nonce"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        $status.removeClass('error').addClass('success').text(response.data).show();
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        $status.removeClass('success').addClass('error').text(response.data || 'Erro ao restaurar').show();
                    }
                },
                error: function() {
                    $status.removeClass('success').addClass('error').text('Erro de conexão').show();
                },
                complete: function() {
                    $button.prop('disabled', false);
                    setTimeout(function() {
                        $status.fadeOut();
                    }, 3000);
                }
            });
        }
    });
});
</script>
