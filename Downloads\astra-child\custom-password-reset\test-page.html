<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Custom Password Reset</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/password-reset.css">
</head>
<body class="cpr-password-reset cpr-theme-dark">
    
    <div class="cpr-container">
        <!-- Logo -->
        <div class="cpr-logo-container">
            <div class="cpr-logo-icon">
                <i class="fas fa-envelope"></i>
            </div>
        </div>
        
        <!-- Card -->
        <div class="cpr-card">
            <!-- Título -->
            <h1 class="cpr-title">Recuperar <PERSON></h1>
            <p class="cpr-subtitle">Digite seu e-mail corporativo para receber as instruções de redefinição de senha.</p>
            
            <!-- Mensagem de exemplo -->
            <div class="cpr-message cpr-message-success" style="display: none;">
                <i class="fas fa-check-circle"></i>
                Instruções enviadas com sucesso!
            </div>
            
            <!-- Formulário -->
            <form class="cpr-form" onsubmit="return false;">
                <div class="cpr-form-group">
                    <label for="user_login" class="cpr-label">
                        Nome de usuário ou endereço de e-mail
                    </label>
                    <input 
                        type="text" 
                        name="user_login" 
                        id="user_login" 
                        class="cpr-input" 
                        placeholder="E-mail corporativo"
                        required
                    >
                </div>
                
                <button type="submit" class="cpr-button">
                    <i class="fas fa-paper-plane"></i>
                    Enviar Instruções
                </button>
            </form>
            
            <!-- Link para voltar ao login -->
            <div class="cpr-footer">
                <a href="#" class="cpr-link">
                    <i class="fas fa-arrow-left"></i>
                    Voltar ao Login
                </a>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Configurações para o JavaScript
        var cprSettings = {
            theme: 'dark',
            ajaxUrl: '#',
            nonce: 'test'
        };
    </script>
    <script src="assets/js/password-reset.js"></script>
    
    <!-- Controles de teste -->
    <div style="position: fixed; top: 10px; left: 10px; background: rgba(0,0,0,0.8); color: white; padding: 15px; border-radius: 8px; font-family: monospace; z-index: 1000;">
        <h4 style="margin: 0 0 10px 0;">Controles de Teste</h4>
        <button onclick="toggleTheme()" style="margin: 5px; padding: 8px 12px; border: none; border-radius: 4px; cursor: pointer;">
            Alternar Tema
        </button>
        <button onclick="showSuccess()" style="margin: 5px; padding: 8px 12px; border: none; border-radius: 4px; cursor: pointer;">
            Mostrar Sucesso
        </button>
        <button onclick="showError()" style="margin: 5px; padding: 8px 12px; border: none; border-radius: 4px; cursor: pointer;">
            Mostrar Erro
        </button>
        <button onclick="testValidation()" style="margin: 5px; padding: 8px 12px; border: none; border-radius: 4px; cursor: pointer;">
            Testar Validação
        </button>
    </div>
    
    <script>
        function toggleTheme() {
            if (window.customPasswordReset) {
                const currentTheme = window.customPasswordReset.getTheme();
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                window.customPasswordReset.setTheme(newTheme);
            }
        }
        
        function showSuccess() {
            $('.cpr-message').hide();
            $('.cpr-message-success').show();
            setTimeout(() => $('.cpr-message-success').fadeOut(), 3000);
        }
        
        function showError() {
            $('.cpr-message').hide();
            const errorMsg = $('<div class="cpr-message cpr-message-error"><i class="fas fa-exclamation-circle"></i> Erro de teste!</div>');
            $('.cpr-subtitle').after(errorMsg);
            setTimeout(() => errorMsg.fadeOut(), 3000);
        }
        
        function testValidation() {
            $('#user_login').val('email-invalido').trigger('blur');
            setTimeout(() => {
                $('#user_login').val('<EMAIL>').trigger('blur');
            }, 2000);
        }
        
        // Simular envio do formulário
        $('.cpr-form').on('submit', function(e) {
            e.preventDefault();
            const $button = $('.cpr-button');
            
            $button.addClass('cpr-loading').text('Enviando...');
            
            setTimeout(() => {
                $button.removeClass('cpr-loading').html('<i class="fas fa-paper-plane"></i> Enviar Instruções');
                showSuccess();
            }, 2000);
        });
    </script>
</body>
</html>
