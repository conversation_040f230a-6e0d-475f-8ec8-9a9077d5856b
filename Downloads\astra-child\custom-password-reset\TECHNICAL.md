# Custom Password Reset - Documentação Técnica

## 🏗️ Arquitetura do Plugin

### Estrutura de Classes

```php
CustomPasswordReset (Classe Principal)
├── __construct()           # Inicialização
├── init_hooks()           # Configuração de hooks
├── enqueue_assets()       # Assets frontend
├── enqueue_login_assets() # Assets login override
├── enqueue_admin_assets() # Assets painel admin
├── custom_lost_password_page()    # Intercepta formulário
├── custom_reset_password_page()   # Intercepta reset
├── process_custom_lost_password() # Processa envio
├── process_custom_reset_password() # Processa reset
├── add_admin_menu()       # Menu administrativo
├── admin_page()           # Página de configurações
├── save_settings_ajax()   # Salvar via AJAX
├── reset_settings_ajax()  # Reset via AJAX
└── shortcode_password_reset() # Shortcode [custom_password_reset]

CustomPasswordResetInstaller (Classe de Instalação)
├── activate()             # Ativação do plugin
├── deactivate()          # Desativação
├── uninstall()           # Desinstalação
├── upgrade()             # Atualizações
├── create_default_options() # Opções padrão
├── check_dependencies()   # Verificar dependências
├── create_pages()        # Criar páginas
└── clear_cache()         # Limpar cache
```

## 🔧 Hooks e Filtros

### Actions (Ações)
```php
// Frontend
add_action('login_form_lostpassword', [$this, 'custom_lost_password_page']);
add_action('login_form_retrievepassword', [$this, 'custom_lost_password_page']);
add_action('login_form_resetpass', [$this, 'custom_reset_password_page']);
add_action('login_form_rp', [$this, 'custom_reset_password_page']);

// Assets
add_action('wp_enqueue_scripts', [$this, 'enqueue_assets']);
add_action('login_enqueue_scripts', [$this, 'enqueue_login_assets']);
add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);

// Processamento
add_action('wp_loaded', [$this, 'process_custom_lost_password']);
add_action('wp_loaded', [$this, 'process_custom_reset_password']);

// Admin
add_action('admin_menu', [$this, 'add_admin_menu']);
add_action('admin_init', [$this, 'register_settings']);

// AJAX
add_action('wp_ajax_cpr_save_settings', [$this, 'save_settings_ajax']);
add_action('wp_ajax_cpr_reset_settings', [$this, 'reset_settings_ajax']);
```

### Shortcodes
```php
add_shortcode('custom_password_reset', [$this, 'shortcode_password_reset']);
```

## 🎨 Sistema de Temas

### Variáveis CSS (CSS Custom Properties)
```css
:root {
    --cpr-primary-bg: #1a1a1a;      /* Fundo principal */
    --cpr-card-bg: #2d2d2d;         /* Fundo do card */
    --cpr-text-color: #ffffff;       /* Cor do texto */
    --cpr-text-secondary: #b0b0b0;   /* Texto secundário */
    --cpr-button-bg: #4a90e2;        /* Fundo do botão */
    --cpr-button-text: #ffffff;      /* Texto do botão */
    --cpr-button-hover: #357abd;     /* Hover do botão */
    --cpr-input-bg: #3a3a3a;         /* Fundo do input */
    --cpr-input-border: #555555;     /* Borda do input */
    --cpr-input-focus: #4a90e2;      /* Focus do input */
    --cpr-error-color: #ff6b6b;      /* Cor de erro */
    --cpr-success-color: #51cf66;    /* Cor de sucesso */
    --cpr-border-radius: 8px;        /* Raio das bordas */
    --cpr-shadow: 0 4px 20px rgba(0, 0, 0, 0.3); /* Sombra */
}
```

### Classes de Tema
```css
.cpr-theme-dark   /* Tema escuro (padrão) */
.cpr-theme-light  /* Tema claro */
```

## 📱 Sistema Responsivo

### Breakpoints
```css
/* Desktop */
@media (min-width: 769px) { /* Estilos desktop */ }

/* Tablet */
@media (max-width: 768px) and (min-width: 481px) { /* Estilos tablet */ }

/* Mobile */
@media (max-width: 480px) { /* Estilos mobile */ }
```

### Ajustes Responsivos
- Padding e margens reduzidos
- Fontes menores em mobile
- Botão de tema menor
- Layout de uma coluna

## 🔒 Segurança

### Nonces
```php
// Formulário de esqueceu senha
wp_nonce_field('cpr_lost_password', 'cpr_nonce');

// Formulário de reset
wp_nonce_field('cpr_reset_password', 'cpr_nonce');

// Admin AJAX
wp_nonce_field('cpr_admin_nonce', 'nonce');
```

### Sanitização
```php
// Texto
sanitize_text_field($_POST['field']);

// Email
sanitize_email($_POST['email']);

// URL
esc_url($_POST['url']);

// HTML
esc_html($text);

// Atributos
esc_attr($attr);
```

### Validação
```php
// Verificar nonce
if (!wp_verify_nonce($_POST['cpr_nonce'], 'cpr_lost_password')) {
    wp_die('Erro de segurança');
}

// Verificar permissões
if (!current_user_can('manage_options')) {
    wp_die('Unauthorized');
}
```

## 🗄️ Banco de Dados

### Opções do WordPress
```php
// Configurações principais
cpr_theme                    # Tema padrão (dark/light)
cpr_logo_url                # URL da logo
cpr_background_color        # Cor de fundo
cpr_card_background         # Cor do card
cpr_text_color              # Cor do texto
cpr_button_color            # Cor do botão
cpr_button_text_color       # Cor do texto do botão
cpr_title                   # Título principal
cpr_subtitle                # Subtítulo
cpr_button_text             # Texto do botão
cpr_back_to_login_text      # Texto "voltar ao login"
cpr_custom_page_slug        # Slug da página personalizada
cpr_enable_custom_page      # Habilitar página personalizada
cpr_custom_css              # CSS personalizado
cpr_custom_js               # JavaScript personalizado

// Metadados
cpr_version                 # Versão do plugin
cpr_installed_date          # Data de instalação
cpr_lost_password_page_id   # ID da página de esqueceu senha
cpr_reset_password_page_id  # ID da página de reset
```

## 🌐 AJAX Endpoints

### Salvar Configurações
```javascript
// Endpoint: wp_ajax_cpr_save_settings
$.ajax({
    url: ajaxurl,
    type: 'POST',
    data: {
        action: 'cpr_save_settings',
        nonce: nonce,
        settings: settingsObject
    }
});
```

### Reset Configurações
```javascript
// Endpoint: wp_ajax_cpr_reset_settings
$.ajax({
    url: ajaxurl,
    type: 'POST',
    data: {
        action: 'cpr_reset_settings',
        nonce: nonce
    }
});
```

## 🎯 JavaScript API

### Classe Principal
```javascript
class CustomPasswordReset {
    constructor()               # Inicialização
    init()                     # Configuração inicial
    setupTheme()               # Configurar tema
    setupThemeToggle()         # Botão de alternar tema
    setupFormValidation()      # Validação de formulário
    setupFormSubmission()      # Envio de formulário
    setupAnimations()          # Animações
    setupAccessibility()       # Acessibilidade
    toggleTheme()              # Alternar tema
    validateForm()             # Validar formulário
    showLoadingState()         # Estado de carregamento
    setTheme(theme)            # Definir tema
    getTheme()                 # Obter tema atual
}
```

### Métodos Públicos
```javascript
// Alterar tema
window.customPasswordReset.setTheme('dark');

// Obter tema atual
const theme = window.customPasswordReset.getTheme();
```

## 🔧 Integração com WordPress

### Interceptação de URLs
```php
// URLs interceptadas:
/wp-login.php?action=lostpassword
/wp-login.php?action=retrievepassword
/wp-login.php?action=resetpass
/wp-login.php?action=rp
```

### Processamento Nativo
```php
// Usa funções nativas do WordPress:
retrieve_password($user_login);          # Enviar email de reset
check_password_reset_key($key, $login);  # Verificar chave
reset_password($user, $password);        # Redefinir senha
```

## 🎨 Customização Avançada

### CSS Personalizado
Adicione CSS na aba "Avançado" do painel administrativo:
```css
/* Exemplo: Customizar botão */
.cpr-button {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    border-radius: 25px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}
```

### JavaScript Personalizado
Adicione JavaScript na aba "Avançado":
```javascript
// Exemplo: Validação personalizada
jQuery(document).ready(function($) {
    $('.cpr-input').on('blur', function() {
        // Sua validação personalizada
    });
});
```

## 🧪 Testes

### Arquivo de Teste
Use `test-page.html` para testar o design:
```bash
# Abrir no navegador
open custom-password-reset/test-page.html
```

### Controles de Teste
- Alternar tema
- Mostrar mensagens de sucesso/erro
- Testar validação
- Simular envio de formulário

## 🚀 Performance

### Otimizações
- CSS/JS minificados em produção
- Carregamento condicional de assets
- Cache de configurações
- Lazy loading de componentes

### Cache Compatibility
Compatível com:
- WP Rocket
- W3 Total Cache
- WP Super Cache
- LiteSpeed Cache
- SG Optimizer
- Cache Enabler

## 🔍 Debug

### Logs
```php
// Ativação
error_log('Custom Password Reset Plugin ativado com sucesso');

// Processamento
error_log('Processando reset de senha para: ' . $user_login);

// Erros
error_log('Erro no Custom Password Reset: ' . $error_message);
```

### Constantes de Debug
```php
// No wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('SCRIPT_DEBUG', true);
```

## 📋 Checklist de Implementação

- [x] Estrutura básica do plugin
- [x] Sistema de temas (dark/light)
- [x] Formulários personalizados
- [x] Painel administrativo
- [x] Validação e segurança
- [x] Responsividade
- [x] Acessibilidade
- [x] Integração com tema
- [x] Sistema de instalação
- [x] Documentação
- [x] Arquivo de teste
- [x] Compatibilidade com cache

## 🎯 Próximos Passos

1. Testar em diferentes navegadores
2. Validar acessibilidade com ferramentas
3. Otimizar performance
4. Adicionar mais temas
5. Implementar analytics
6. Criar testes automatizados
